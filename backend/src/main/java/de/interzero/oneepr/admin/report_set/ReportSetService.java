package de.interzero.oneepr.admin.report_set;

import de.interzero.oneepr.admin.fraction_icon.Files;
import de.interzero.oneepr.admin.fraction_icon.FilesRepository;
import de.interzero.oneepr.admin.fraction_icon.FractionIcon;
import de.interzero.oneepr.admin.fraction_icon.FractionIconRepository;
import de.interzero.oneepr.admin.packaging_service.PackagingService;
import de.interzero.oneepr.admin.packaging_service.PackagingServiceRepository;
import de.interzero.oneepr.admin.price_list.ReportSetPriceListItem;
import de.interzero.oneepr.admin.report_set.dto.*;
import de.interzero.oneepr.admin.report_set_column_fractions.ReportSetColumnFraction;
import de.interzero.oneepr.admin.report_set_column_fractions.ReportSetColumnFractionRepository;
import de.interzero.oneepr.admin.report_set_columns.ReportSetColumn;
import de.interzero.oneepr.admin.report_set_columns.ReportSetColumnRepository;
import de.interzero.oneepr.admin.report_set_fractions.ReportSetFraction;
import de.interzero.oneepr.admin.report_set_fractions.ReportSetFractionRepository;
import de.interzero.oneepr.admin.report_set_price_list.ReportSetPriceList;
import de.interzero.oneepr.admin.report_set_price_list.ReportSetPriceListRepository;
import de.interzero.oneepr.common.config.ModelMapperConfig;
import de.interzero.oneepr.common.service.AwsService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.server.ResponseStatusException;

import java.time.*;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@SuppressWarnings("java:S6539")
public class ReportSetService {

    private final ReportSetRepository reportSetRepository;

    private final ReportSetFractionRepository reportSetFractionRepository;

    private final ReportSetColumnRepository reportSetColumnRepository;

    private final ReportSetColumnFractionRepository reportColumnFractionRepository;

    private final ReportSetPriceListRepository reportSetPriceListRepository;

    private final ReportSetPriceListItemRepository reportSetPriceListItemRepository;

    private final FilesRepository filesRepository;

    private final EntityManager entityManager;

    private final FractionIconRepository fractionIconRepository;

    private final PackagingServiceRepository packagingServiceRepository;

    private final AwsService awsService;

    private static final String REPORT_SET_NOT_FOUND = "Report set not found";

    private static final String REPORT_SET_FRACTION_ICON_NOT_FOUND = "Report set fraction icon not found";

    private static final String PACKAGING_SERVICE_NOT_FOUND = "PackagingService not found";

    private static final String DELETED_AT = "deletedAt";

    /**
     * Creates and persists a new ReportSet based on the provided DTO using ModelMapper.
     *
     * @param data The data transfer object containing the details for the new report set.
     * @return The newly created and persisted ReportSet entity.
     */
    @Transactional
    public ReportSet create(ReportSetCreateDto data) {
        ReportSet reportSet = ModelMapperConfig.mapPresentFields(data, ReportSet.class);
        if (data.getPackagingServiceId() != null) {
            PackagingService packagingService = packagingServiceRepository.findById(data.getPackagingServiceId())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, PACKAGING_SERVICE_NOT_FOUND));
            reportSet.setPackagingService(packagingService);
        }
        return reportSetRepository.save(reportSet);
    }

    /**
     * @param query packaging_service_id and is_delete is Null
     * @return A list of Report Set.
     */
    public List<ReportSet> findAll(ReportSetFindAllDto query) {
        return reportSetRepository.findAll(byPackagingServiceIdAndNotDeleted(query.getPackagingServiceId()));
    }

    /**
     * find report set by id
     *
     * @param id report set id
     * @return report set
     */
    public ReportSet findOne(Integer id) {
        return reportSetRepository.findReportSetByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, REPORT_SET_NOT_FOUND));
    }

    /**
     * Update By id and UpdateReportSetDto
     * SuppressWarnings for code complexity
     *
     * @param id   report set id
     * @param data UpdateReportSetDto
     * @return report set
     */
    @Transactional
    @SuppressWarnings({"java:S3776", "java:S6541"})
    public ReportSetDetailDto update(Integer id,
                                     UpdateReportSetDto data) {

        ReportSet reportSet = reportSetRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, REPORT_SET_NOT_FOUND));
        List<ReportSetFraction> reportSetFractions = reportSetFractionRepository.findByReportSet_Id(id);
        List<ReportSetColumn> reportSetColumns = reportSetColumnRepository.findByReportSet_Id(id);
        List<String> fractionCode = reportSetFractions.stream().map(ReportSetFraction::getCode).toList();
        List<String> columnCode = reportSetColumns.stream().map(ReportSetColumn::getCode).toList();

        reportSet.setId(id);
        reportSet.setName(data.getName());
        if (data.getSheetFileId() != null) {
            reportSet.setSheetFile(filesRepository.findById(data.getSheetFileId()).orElse(null));
        }
        reportSet.setSheetFileDescription(data.getSheetFileDescription());
        reportSetRepository.save(reportSet);

        List<Integer> priceListIds = Optional.ofNullable(data.getPriceLists())
                .orElseGet(ArrayList::new)
                .stream()
                .map(ReportSetPriceListDto::getId)
                .toList();

        if (!priceListIds.isEmpty()) {
            reportSetPriceListItemRepository.deleteByFractionCodeIn(fractionCode);
        }

        if (!CollectionUtils.isEmpty(data.getColumns())) {
            reportColumnFractionRepository.deleteReportSetColumnFractionsByColumnCodeIn(columnCode);
        }

        reportSetFractionRepository.deleteByReportSetId(id);
        reportSetColumnRepository.deleteByReportSetId(id);

        // --- FRACTIONS PROCESSING (Consolidated) ---
        Map<String, ReportSetFraction> fractionsByCode = new HashMap<>();
        if (!CollectionUtils.isEmpty(data.getFractions())) {
            // PASS 1: Create all fraction entities in memory
            for (ReportSetFractionCreateDto fractionDto : data.getFractions()) {
                ReportSetFraction reportSetFraction = new ReportSetFraction();
                reportSetFraction.setCode(fractionDto.getCode());
                reportSetFraction.setName(fractionDto.getName());
                reportSetFraction.setDescription(fractionDto.getDescription());
                reportSetFraction.setIcon(fractionDto.getIcon());
                reportSetFraction.setLevel(fractionDto.getLevel());
                reportSetFraction.setOrder(fractionDto.getOrder());
                reportSetFraction.setHasSecondLevel(fractionDto.getHasSecondLevel());
                reportSetFraction.setHasThirdLevel(fractionDto.getHasThirdLevel());

                if (fractionDto.getFractionIconId() != null) {
                    reportSetFraction.setFractionIcon(fractionIconRepository.findById(fractionDto.getFractionIconId())
                                                              .orElse(null));
                }

                // Use the helper method to link to the parent ReportSet
                reportSet.addFraction(reportSetFraction);
                fractionsByCode.put(fractionDto.getCode(), reportSetFraction);
            }
            // PASS 2: Link fraction hierarchies in memory
            for (ReportSetFractionCreateDto fractionDto : data.getFractions()) {
                if (fractionDto.getParentCode() != null) {
                    ReportSetFraction child = fractionsByCode.get(fractionDto.getCode());
                    ReportSetFraction parent = fractionsByCode.get(fractionDto.getParentCode());
                    if (child != null && parent != null) {
                        child.setParent(parent);
                    }
                }
            }

            List<ReportSetFraction> managedFractions = reportSetFractionRepository.saveAll(reportSet.getFractions());
            for (ReportSetFraction fraction : managedFractions) {
                reportSet.addFraction(fraction);
            }
            fractionsByCode = managedFractions.stream().collect(Collectors.toMap(ReportSetFraction::getCode, f -> f));
        }
        Map<String, ReportSetColumn> columnsByCode = new HashMap<>();
        if (!CollectionUtils.isEmpty(data.getColumns())) {
            // PASS 1: Create all column entities in memory
            for (ReportSetColumnCreateDto columnDto : data.getColumns()) {
                ReportSetColumn reportSetColumn = new ReportSetColumn();
                reportSetColumn.setCode(columnDto.getCode());
                reportSetColumn.setName(columnDto.getName());
                reportSetColumn.setDescription(columnDto.getDescription());
                reportSetColumn.setUnitType(columnDto.getUnitType());
                reportSetColumn.setLevel(columnDto.getLevel());
                reportSetColumn.setOrder(columnDto.getOrder());

                // Use the helper method to link to the parent ReportSet
                reportSet.addColumn(reportSetColumn);
                columnsByCode.put(columnDto.getCode(), reportSetColumn);
            }
            // PASS 2: Link column hierarchies in memory
            for (ReportSetColumnCreateDto columnDto : data.getColumns()) {
                if (columnDto.getParentCode() != null) {
                    ReportSetColumn child = columnsByCode.get(columnDto.getCode());
                    ReportSetColumn parent = columnsByCode.get(columnDto.getParentCode());
                    if (child != null && parent != null) {
                        child.setParent(parent);
                    }
                }
            }
            List<ReportSetColumn> managedColumns = reportSetColumnRepository.saveAll(reportSet.getColumns());
            for (ReportSetColumn column : managedColumns) {
                reportSet.addColumn(column);
            }
        }

        // --- FINAL SAVE OPERATION ---
        // After all in-memory object graphs are correctly built,
        // a single save on the root entity will cascade all changes.
        reportSetRepository.save(reportSet);
        entityManager.flush(); // Flush to ensure fractions/columns are in DB for the next step.

        // --- JOIN TABLE: PASS 3 ---
        if (!CollectionUtils.isEmpty(data.getColumns())) {
            List<ReportSetColumnFraction> allColumnFractions = new ArrayList<>();
            for (ReportSetColumnCreateDto columnDto : data.getColumns()) {
                if (!CollectionUtils.isEmpty(columnDto.getFractions())) {
                    // Look up the now-persisted column from our IN-MEMORY map
                    ReportSetColumn columnEntity = columnsByCode.get(columnDto.getCode());

                    for (ReportSetColumnFractionDetailDto cfDto : columnDto.getFractions()) {
                        // Look up the now-persisted fraction from our IN-MEMORY map
                        ReportSetFraction fractionEntity = fractionsByCode.get(cfDto.getFractionCode());

                        if (columnEntity != null && fractionEntity != null) {
                            ReportSetColumnFraction columnFraction = new ReportSetColumnFraction();
                            columnFraction.setReportSetColumn(columnEntity);
                            columnFraction.setReportSetFraction(fractionEntity);
                            allColumnFractions.add(columnFraction);
                        }
                    }
                }
            }

            if (!allColumnFractions.isEmpty()) {
                reportColumnFractionRepository.saveAll(allColumnFractions);
            }
        }

        if (data.getPriceLists() != null) {
            for (ReportSetPriceListDto dto : data.getPriceLists()) {
                // Step 1: Create and map the parent ReportSetPriceList entity from the DTO.
                ReportSetPriceList priceListEntity = new ReportSetPriceList();

                // Handle upsert logic: if an ID is provided, fetch the existing entity.
                if (dto.getId() != null && dto.getId() > 0) {
                    priceListEntity = reportSetPriceListRepository.findById(dto.getId()).orElse(priceListEntity);
                }
                priceListEntity.setTitle(dto.getTitle());
                LocalDate startDateDto = dto.getStartDate();
                LocalDate endDateDto = dto.getEndDate();
                Instant startDateInstant = startDateDto.atStartOfDay(ZoneOffset.UTC).toInstant();
                Instant endDateInstant = endDateDto.atTime(LocalTime.MAX).atZone(ZoneOffset.UTC).toInstant();
                priceListEntity.setStartDate(startDateInstant);
                priceListEntity.setEndDate(endDateInstant);
                priceListEntity.setType(dto.getType());
                priceListEntity.setFixedPrice(dto.getFixedPrice());
                priceListEntity.setBasePrice(dto.getBasePrice());
                priceListEntity.setMinimumFee(dto.getMinimumFee());
                priceListEntity.setLicenseYear(dto.getLicenseYear());
                priceListEntity.setReportSet(reportSet);
                reportSet.addPriceList(priceListEntity);
                // Step 2: Save the parent entity FIRST to get a managed instance with a generated ID.
                ReportSetPriceList savedPriceListEntity = reportSetPriceListRepository.save(priceListEntity);

                // Step 3: Now create and save the child items, linking them to the SAVED parent.
                if (dto.getItems() != null && !dto.getItems().isEmpty()) {
                    List<ReportSetPriceListItem> itemEntities = dto.getItems().stream().map(itemDto -> {
                        ReportSetPriceListItem itemEntity = new ReportSetPriceListItem();
                        itemEntity.setPrice(itemDto.getPrice());
                        itemEntity.setReportSetFraction(reportSetFractionRepository.findByCode(itemDto.getFractionCode())
                                                                .orElseThrow(() -> new ResponseStatusException(
                                                                        HttpStatus.NOT_FOUND,
                                                                        itemDto.getFractionCode() + REPORT_SET_FRACTION_ICON_NOT_FOUND)));

                        itemEntity.setPriceList(savedPriceListEntity);
                        savedPriceListEntity.addItem(itemEntity);
                        return itemEntity;
                    }).toList();

                    // Step 4: Save the fully constructed and linked child items.
                    reportSetPriceListItemRepository.saveAllAndFlush(itemEntities);
                }
            }
        }
        ReportSet finalReportSet = reportSetRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, REPORT_SET_NOT_FOUND));
        boolean hasCriteria = !finalReportSet.getPackagingService().getCriterias().isEmpty();
        return convertToDetailDto(finalReportSet, hasCriteria);
    }

    /**
     * Converts a ReportSet entity to its detailed DTO representation. This is the primary
     * method to call from within a transactional context to ensure all lazy-loaded
     * collections are properly initialized and mapped.
     *
     * @param reportSet   The ReportSet entity to convert, fetched within an active transaction.
     * @param hasCriteria The pre-calculated hasCriteria flag.
     * @return A fully populated ReportSetDetailDto ready for API response.
     */
    public ReportSetDetailDto convertToDetailDto(ReportSet reportSet,
                                                 boolean hasCriteria) {
        if (reportSet == null) {
            return null;
        }

        ReportSetDetailDto dto = new ReportSetDetailDto();

        // Map scalar fields
        dto.setId(reportSet.getId());
        dto.setName(reportSet.getName());
        dto.setMode(reportSet.getMode());
        dto.setType(reportSet.getType());
        dto.setCreatedAt(reportSet.getCreatedAt());
        dto.setUpdatedAt(reportSet.getUpdatedAt());
        dto.setDeletedAt(reportSet.getDeletedAt());
        dto.setSheetFileDescription(reportSet.getSheetFileDescription());
        dto.setPackagingServiceId(reportSet.getPackagingServiceId());
        if (reportSet.getPackagingService() != null) {
            dto.setPackagingService(convertToPackagingServiceDto(reportSet.getPackagingService()));
        }
        // Map direct relations by calling their respective converters
        dto.setSheetFile(convertToFileDto(reportSet.getSheetFile()));
        dto.setHasCriteria(hasCriteria);

        // Map collections, triggering lazy loads within the transaction
        if (reportSet.getColumns() != null) {
            dto.setColumns(reportSet.getColumns().stream().map(this::convertToColumnDto).toList());
        }
        if (reportSet.getFractions() != null) {
            dto.setFractions(reportSet.getFractions().stream().map(this::convertToFractionDto).toList());
        }
        if (reportSet.getPriceLists() != null) {
            dto.setPriceLists(reportSet.getPriceLists().stream().map(this::convertToPriceListDto).toList());
        }

        return dto;
    }

    private PackagingServiceDto convertToPackagingServiceDto(PackagingService entity) {
        if (entity == null) {
            return null;
        }
        PackagingServiceDto dto = new PackagingServiceDto();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        return dto;
    }

    private FilesDetailDto convertToFileDto(Files entity) {
        if (entity == null) {
            return null;
        }
        FilesDetailDto dto = new FilesDetailDto();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setExtension(entity.getExtension());
        dto.setSize(entity.getSize());
        dto.setCreatorType(entity.getCreatorType());
        dto.setDocumentType(entity.getDocumentType());
        dto.setUserId(entity.getUserId());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setOriginalName(entity.getOriginalName());
        dto.setCountryId(entity.getCountryId());
        return dto;
    }

    // --- Collection Converters (with recursion and nested objects) ---

    private ReportSetColumnDetailDto convertToColumnDto(ReportSetColumn entity) {
        if (entity == null) {
            return null;
        }
        ReportSetColumnDetailDto dto = new ReportSetColumnDetailDto();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setDescription(entity.getDescription());
        dto.setUnitType(entity.getUnitType());
        dto.setParentId(entity.getParentId());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setDeletedAt(entity.getDeletedAt());
        dto.setLevel(entity.getLevel());
        dto.setOrder(entity.getOrder());
        dto.setCode(entity.getCode());
        dto.setReportSetId(entity.getReportSetId());
        dto.setParentCode(entity.getParentCode());
        if (entity.getChildren() != null) {
            dto.setChildren(entity.getChildren().stream().map(this::convertToColumnDto).toList());
        }
        if (entity.getFractions() != null) {
            dto.setFractions(entity.getFractions().stream().map(this::convertToColumnFractionDto).toList());
        }
        return dto;
    }

    private ReportSetFractionDetailDto convertToFractionDto(ReportSetFraction entity) {
        if (entity == null) {
            return null;
        }
        ReportSetFractionDetailDto dto = new ReportSetFractionDetailDto();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setDescription(entity.getDescription());
        dto.setParentId(entity.getParentId());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setDeletedAt(entity.getDeletedAt());
        dto.setIsActive(entity.getIsActive());
        dto.setLevel(entity.getLevel());
        dto.setFractionIconId(entity.getFractionIconId());
        dto.setOrder(entity.getOrder());
        dto.setCode(entity.getCode());
        dto.setIcon(entity.getIcon());
        dto.setHasSecondLevel(entity.getHasSecondLevel());
        dto.setHasThirdLevel(entity.getHasThirdLevel());
        dto.setReportSetId(entity.getReportSetId());
        dto.setParentCode(entity.getParentCode());
        dto.setFractionIcon(convertToFractionIconDto(entity.getFractionIcon()));
        if (entity.getChildren() != null) {
            dto.setChildren(entity.getChildren().stream().map(this::convertToFractionDto).collect(Collectors.toSet()));
        }
        return dto;
    }

    private ReportSetPriceListDetailDto convertToPriceListDto(ReportSetPriceList entity) {

        if (entity == null) {
            return null;
        }
        ReportSetPriceListDetailDto dto = new ReportSetPriceListDetailDto();
        dto.setId(entity.getId());
        dto.setTitle(entity.getTitle());
        dto.setStartDate(entity.getStartDate());
        dto.setEndDate(entity.getEndDate());
        dto.setBasePrice(entity.getBasePrice());
        dto.setFixedPrice(entity.getFixedPrice());
        dto.setMinimumFee(entity.getMinimumFee());
        dto.setType(entity.getType());
        dto.setLicenseYear(entity.getLicenseYear());
        if (!entity.getItems().isEmpty()) {
            dto.setItems(entity.getItems().stream().map(this::convertToPriceListItemDto).toList());
        }
        return dto;
    }

    // --- Innermost and Utility Converters ---

    private ReportSetPriceListItemDetailDto convertToPriceListItemDto(ReportSetPriceListItem entity) {
        if (entity == null) {
            return null;
        }
        ReportSetPriceListItemDetailDto dto = new ReportSetPriceListItemDetailDto();
        dto.setId(entity.getId());
        dto.setPrice(entity.getPrice());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setFractionCode(entity.getFractionCode());
        dto.setPriceListId(entity.getPriceListId());
        return dto;
    }

    private FractionIconDetailDto convertToFractionIconDto(FractionIcon entity) {
        if (entity == null) {
            return null;
        }
        FractionIconDetailDto dto = new FractionIconDetailDto();
        dto.setId(entity.getId());
        dto.setImageUrl(awsService.generatePresignedDownloadUrl(entity.getImageUrl(), Duration.ofMinutes(15))
                                .getPreSignedUrl());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setDeletedAt(entity.getDeletedAt());
        dto.setFileId(entity.getFileId());
        return dto;
    }

    private ReportSetColumnFractionDetailDto convertToColumnFractionDto(ReportSetColumnFraction entity) {
        if (entity == null) {
            return null;
        }
        ReportSetColumnFractionDetailDto dto = new ReportSetColumnFractionDetailDto();
        dto.setId(entity.getId());
        dto.setColumnCode(entity.getColumnCode());
        dto.setFractionCode(entity.getFractionCode());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        return dto;
    }

    /**
     * Duplicates an existing report set with all its related data.
     * This version corrects flaws related to state management and performance by building
     * the entire entity graph in memory before persisting.
     *
     * @param id The ID of the report set to duplicate
     * @return A map indicating success
     */
    @Transactional
    @SuppressWarnings({"java:S3776", "java:S6541"})
    public Map<String, Boolean> duplicate(Integer id) {
        ReportSet reportSet = reportSetRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, REPORT_SET_NOT_FOUND));
        // --- PASS 1: Create the root duplicated entity ---
        ReportSet duplicatedReportSet = new ReportSet();
        duplicatedReportSet.setPackagingService(reportSet.getPackagingService());
        duplicatedReportSet.setName(reportSet.getName() + " - " + CodeGeneratorUtil.generateCode());
        duplicatedReportSet.setMode(reportSet.getMode());
        duplicatedReportSet.setType(reportSet.getType());

        reportSetRepository.save(duplicatedReportSet);

        // --- In-Memory Maps for linking new entities ---
        Map<String, ReportSetFraction> newFractionsByOldCode = new HashMap<>();
        Map<String, ReportSetColumn> newColumnsByOldCode = new HashMap<>();

        // --- PASS 2.1: Create all Fraction objects in memory ---
        List<ReportSetFraction> originalFractions = Optional.ofNullable(reportSet.getFractions())
                .orElse(Collections.emptyList()) // Treat null collection as empty
                .stream()
                .filter(f -> f.getDeletedAt() == null)
                .toList();

        for (ReportSetFraction originalFraction : originalFractions) {
            ReportSetFraction newFraction = new ReportSetFraction();
            newFraction.setName(originalFraction.getName());
            newFraction.setDescription(originalFraction.getDescription());
            newFraction.setIsActive(originalFraction.getIsActive());
            newFraction.setIcon(originalFraction.getIcon());
            newFraction.setLevel(originalFraction.getLevel());
            newFraction.setOrder(originalFraction.getOrder());
            newFraction.setHasSecondLevel(originalFraction.getHasSecondLevel());
            newFraction.setHasThirdLevel(originalFraction.getHasThirdLevel());
            newFraction.setCode(CodeGeneratorUtil.generateCode()); // Generate a new unique code
            if (originalFraction.getFractionIcon() != null) {
                newFraction.setFractionIcon(originalFraction.getFractionIcon()); // Re-use existing icon
            }
            newFraction.setReportSet(duplicatedReportSet);
            duplicatedReportSet.getFractions().add(newFraction);
            newFractionsByOldCode.put(originalFraction.getCode(), newFraction);
        }

        // --- PASS 2.2: Link Fraction hierarchies in memory ---
        for (ReportSetFraction originalFraction : originalFractions) {
            if (originalFraction.getParent() != null) {
                ReportSetFraction newChild = newFractionsByOldCode.get(originalFraction.getCode());
                ReportSetFraction newParent = newFractionsByOldCode.get(originalFraction.getParent().getCode());
                if (newChild != null && newParent != null) {
                    newChild.setParent(newParent);
                }
            }
        }

        // --- PASS 2.3: Create all Column objects in memory ---
        List<ReportSetColumn> originalColumns = Optional.ofNullable(reportSet.getColumns())
                .orElse(Collections.emptyList()) // Treat null collection as empty
                .stream()
                .filter(c -> c.getDeletedAt() == null)
                .toList();

        for (ReportSetColumn originalColumn : originalColumns) {
            ReportSetColumn newColumn = new ReportSetColumn();
            newColumn.setName(originalColumn.getName());
            newColumn.setDescription(originalColumn.getDescription());
            newColumn.setUnitType(originalColumn.getUnitType());
            newColumn.setLevel(originalColumn.getLevel());
            newColumn.setOrder(originalColumn.getOrder());
            newColumn.setCode(CodeGeneratorUtil.generateCode()); // Generate a new unique code
            newColumn.setReportSet(duplicatedReportSet);
            duplicatedReportSet.getColumns().add(newColumn);
            newColumnsByOldCode.put(originalColumn.getCode(), newColumn);
        }

        // --- PASS 2.4: Link Column hierarchies in memory ---
        for (ReportSetColumn originalColumn : originalColumns) {
            if (originalColumn.getParent() != null) {
                ReportSetColumn newChild = newColumnsByOldCode.get(originalColumn.getCode());
                ReportSetColumn newParent = newColumnsByOldCode.get(originalColumn.getParent().getCode());
                if (newChild != null && newParent != null) {
                    newChild.setParent(newParent);
                }
            }
        }

        // --- PASS 3: Batch-save all new Fractions and Columns ---
        // This ensures they are persisted and available for the join table.
        reportSetFractionRepository.saveAll(duplicatedReportSet.getFractions());
        reportSetColumnRepository.saveAll(duplicatedReportSet.getColumns());

        // --- PASS 4: Create and save join table entities ---
        List<ReportSetColumnFraction> newColumnFractions = new ArrayList<>();
        for (ReportSetColumn originalColumn : originalColumns) {
            ReportSetColumn newColumn = newColumnsByOldCode.get(originalColumn.getCode());
            List<ReportSetColumnFraction> originalColFracs = Optional.ofNullable(originalColumn.getFractions())
                    .orElse(Collections.emptyList());

            for (ReportSetColumnFraction originalColFrac : originalColFracs) {
                if (originalColFrac.getDeletedAt() == null) {
                    ReportSetFraction newFraction = newFractionsByOldCode.get(originalColFrac.getReportSetFraction()
                                                                                      .getCode());
                    if (newColumn != null && newFraction != null) {
                        ReportSetColumnFraction newColFrac = new ReportSetColumnFraction();
                        newColFrac.setReportSetColumn(newColumn);
                        newColFrac.setReportSetFraction(newFraction);
                        newColumnFractions.add(newColFrac);
                    }
                }
            }
        }
        reportColumnFractionRepository.saveAll(newColumnFractions);

        // --- PASS 5: Duplicate Price Lists and Items ---
        List<ReportSetPriceList> originalPriceLists = reportSet.getPriceLists()
                .stream()
                .filter(pl -> pl.getDeletedAt() == null)
                .toList();

        // Use lists to prepare for batch saving
        List<ReportSetPriceList> newPriceListsToSave = new ArrayList<>();
        List<ReportSetPriceListItem> newItemsToSave = new ArrayList<>();

        for (ReportSetPriceList originalPriceList : originalPriceLists) {
            ReportSetPriceList newPriceList = new ReportSetPriceList();
            newPriceList.setTitle(originalPriceList.getTitle());
            newPriceList.setStartDate(originalPriceList.getStartDate());
            newPriceList.setEndDate(originalPriceList.getEndDate());
            newPriceList.setType(originalPriceList.getType());
            newPriceList.setFixedPrice(originalPriceList.getFixedPrice());
            newPriceList.setBasePrice(originalPriceList.getBasePrice());
            newPriceList.setMinimumFee(originalPriceList.getMinimumFee());
            newPriceList.setReportSet(duplicatedReportSet);
            ReportSetPriceList savedPriceList = reportSetPriceListRepository.save(newPriceList);
            newPriceListsToSave.add(newPriceList); // Add to list for batch save

            // Duplicate price list items
            if (originalPriceList.getItems() != null) {
                for (ReportSetPriceListItem originalItem : originalPriceList.getItems()) {
                    if (originalItem.getDeletedAt() == null) {
                        ReportSetPriceListItem newItem = new ReportSetPriceListItem();
                        newItem.setPrice(originalItem.getPrice());
                        String originalFractionCode = originalItem.getReportSetFraction().getCode();
                        ReportSetFraction newFraction = newFractionsByOldCode.get(originalFractionCode);

                        if (newFraction == null) {
                            throw new IllegalStateException("Could not find new fraction in memory map for old code: " + originalFractionCode);
                        }

                        newItem.setReportSetFraction(newFraction);
                        newItem.setPriceList(savedPriceList);
                        newItemsToSave.add(newItem);
                    }
                }
            }
        }

        // Persist everything in efficient batch operations at the end.
        reportSetPriceListRepository.saveAll(newPriceListsToSave);
        reportSetPriceListItemRepository.saveAll(newItemsToSave);

        return Map.of("ok", true);
    }

    /**
     * Soft deletes a report set by setting its deleted_at timestamp.
     *
     * @param id The ID of the report set to delete
     */
    @Transactional
    public void remove(Integer id) {
        ReportSet reportSet = reportSetRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, REPORT_SET_NOT_FOUND));

        reportSet.setDeletedAt(Instant.now());
        reportSetRepository.save(reportSet);
    }

    private static Specification<ReportSet> byPackagingServiceIdAndNotDeleted(Integer packagingServiceId) {
        return (root, query, cb) -> {
            Join<ReportSet, ReportSetColumn> columnsJoin = root.join("columns", JoinType.LEFT);
            Join<ReportSet, ReportSetFraction> fractionsJoin = root.join("fractions", JoinType.LEFT);

            Predicate predicate = cb.isNull(root.get(DELETED_AT));

            predicate = cb.and(
                    predicate,
                    cb.or(columnsJoin.isNull(), cb.isNull(columnsJoin.get(DELETED_AT))),
                    cb.or(fractionsJoin.isNull(), cb.isNull(fractionsJoin.get(DELETED_AT))));

            if (packagingServiceId != null) {
                predicate = cb.and(predicate, cb.equal(root.get("packagingService").get("id"), packagingServiceId));
            }

            return predicate;
        };
    }

}
