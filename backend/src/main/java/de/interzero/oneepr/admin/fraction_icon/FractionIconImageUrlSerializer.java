package de.interzero.oneepr.admin.fraction_icon;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.ser.PropertyWriter;
import com.fasterxml.jackson.databind.ser.impl.SimpleBeanPropertyFilter;
import de.interzero.oneepr.common.service.AwsService;

import java.time.Duration;

public class FractionIconImageUrlSerializer extends SimpleBeanPropertyFilter {

    public final AwsService awsService;

    public FractionIconImageUrlSerializer(AwsService awsService) {
        this.awsService = awsService;
    }


    @Override
    public void serializeAsField(Object pojo,
                                 JsonGenerator jgen,
                                 SerializerProvider provider,
                                 PropertyWriter writer) throws Exception {

        if (pojo instanceof FractionIcon icon && "image_url".equals(writer.getName())) {
            //awsService.generatePresignedDownloadUrl will not call any aws api to generate the url,
            // instead it will just generate the url in our local from the access key and access token
            String preSignedUrl = awsService.generatePresignedDownloadUrl(icon.getImageUrl(), Duration.ofMinutes(15))
                    .getPreSignedUrl();
            jgen.writeStringField(writer.getName(), preSignedUrl);
        } else {
            writer.serializeAsField(pojo, jgen, provider);
        }
    }
}
